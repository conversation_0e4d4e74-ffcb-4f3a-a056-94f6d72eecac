import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CSSReset } from "@chakra-ui/react";
import { Provider } from "react-redux";
import store from "./store";
import Navbar from "./components/layout/Navbar";
import Footer from "./components/layout/Footer";
import Home from "./pages/Home";
import Tours from "./pages/Tours";
import Shop from "./pages/Shop";
import About from "./pages/About";
import Chat from "./pages/Chat";
import "./App.css";

function App() {
	return (
		<Provider store={store}>
			<ChakraProvider>
				<CSSReset />
				<Router>
					<div className="App">
						<Navbar />
						<main>
							<Routes>
								<Route path="/" element={<Home />} />
								<Route path="/tours" element={<Tours />} />
								<Route path="/shop" element={<Shop />} />
								<Route path="/about" element={<About />} />
								<Route path="/chat" element={<Chat />} />
							</Routes>
						</main>
						<Footer />
					</div>
				</Router>
			</ChakraProvider>
		</Provider>
	);
}

export default App;
