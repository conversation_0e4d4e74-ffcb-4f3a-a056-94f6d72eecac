import { <PERSON>, <PERSON>lex, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, useColorModeValue } from "@chakra-ui/react";
import { Link as RouterLink } from "react-router-dom";
import { useSelector } from "react-redux";

const NavBar = () => {
	const user = useSelector((state) => state.auth.user);

	return (
		<Box>
			<Flex
				bg={useColorModeValue("white", "gray.800")}
				color={useColorModeValue("gray.600", "white")}
				minH={"60px"}
				py={{ base: 2 }}
				px={{ base: 4 }}
				borderBottom={1}
				borderStyle={"solid"}
				borderColor={useColorModeValue("gray.200", "gray.900")}
				align={"center"}
			>
				<Flex flex={{ base: 1 }} justify={{ base: "center", md: "start" }}>
					<Text
						textAlign={useColorModeValue("left", "center")}
						fontFamily={"heading"}
						color={useColorModeValue("gray.800", "white")}
						as={RouterLink}
						to="/"
					>
						Tourism App
					</Text>

					<Flex display={{ base: "none", md: "flex" }} ml={10}>
						<Stack direction={"row"} spacing={4}>
							<Link as={RouterLink} to="/tours" px={2}>
								Tours
							</Link>
							<Link as={RouterLink} to="/shop" px={2}>
								Shop
							</Link>
							<Link as={RouterLink} to="/about" px={2}>
								About
							</Link>
							{user && (
								<Link as={RouterLink} to="/chat" px={2}>
									Chat
								</Link>
							)}
						</Stack>
					</Flex>
				</Flex>

				<Stack
					flex={{ base: 1, md: 0 }}
					justify={"flex-end"}
					direction={"row"}
					spacing={6}
				>
					{user ? (
						<Button
							as={RouterLink}
							to="/profile"
							fontSize={"sm"}
							fontWeight={400}
							variant={"link"}
						>
							Profile
						</Button>
					) : (
						<>
							<Button
								as={RouterLink}
								to="/login"
								fontSize={"sm"}
								fontWeight={400}
								variant={"link"}
							>
								Sign In
							</Button>
							<Button
								as={RouterLink}
								to="/register"
								display={{ base: "none", md: "inline-flex" }}
								fontSize={"sm"}
								fontWeight={600}
								color={"white"}
								bg={"blue.400"}
								_hover={{
									bg: "blue.300",
								}}
							>
								Sign Up
							</Button>
						</>
					)}
				</Stack>
			</Flex>
		</Box>
	);
};

export default NavBar;
