import { Box, Heading, Container, Text, But<PERSON>, Stack, useColorModeValue } from "@chakra-ui/react";
import { Link as RouterLink } from "react-router-dom";

export default function Home() {
	return (
		<>
			<Container maxW={"3xl"}>
				<Stack
					as={Box}
					textAlign={"center"}
					spacing={{ base: 8, md: 14 }}
					py={{ base: 20, md: 36 }}
				>
					<Heading
						fontWeight={600}
						fontSize={{ base: "2xl", sm: "4xl", md: "6xl" }}
						lineHeight={"110%"}
					>
						Discover Amazing <br />
						<Text as={"span"} color={"blue.400"}>
							Travel Experiences
						</Text>
					</Heading>
					<Text color={"gray.500"}>
						Explore unique destinations, connect with local communities, and create
						unforgettable memories. Book tours, shop local products, and engage with
						experienced guides - all in one place.
					</Text>
					<Stack
						direction={"column"}
						spacing={3}
						align={"center"}
						alignSelf={"center"}
						position={"relative"}
					>
						<Button
							as={RouterLink}
							to="/tours"
							colorScheme={"blue"}
							bg={"blue.400"}
							rounded={"full"}
							px={6}
							_hover={{
								bg: "blue.500",
							}}
						>
							Explore Tours
						</Button>
						<Button
							as={RouterLink}
							to="/shop"
							variant={"link"}
							colorScheme={"blue"}
							size={"sm"}
						>
							Visit Shop
						</Button>
					</Stack>
				</Stack>
			</Container>

			{/* Featured Tours Section */}
			<Box bg={useColorModeValue("gray.50", "gray.900")} p={8}>
				<Container maxW={"6xl"}>
					<Heading textAlign={"center"} fontSize={"3xl"} py={10} fontWeight={"bold"}>
						Popular Tours
					</Heading>
					{/* Add FeaturedTours component here */}
				</Container>
			</Box>

			{/* Latest Shop Items Section */}
			<Box p={8}>
				<Container maxW={"6xl"}>
					<Heading textAlign={"center"} fontSize={"3xl"} py={10} fontWeight={"bold"}>
						Local Products
					</Heading>
					{/* Add ShopItems component here */}
				</Container>
			</Box>
		</>
	);
}
