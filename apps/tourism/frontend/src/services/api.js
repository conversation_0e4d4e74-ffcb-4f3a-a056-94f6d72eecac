import axios from "axios";

const API_URL = "/api/method/tourism";

const api = axios.create({
	baseURL: API_URL,
	withCredentials: true,
	headers: {
		"Content-Type": "application/json",
	},
});

// Add request interceptor to handle Frappe CSRF token
api.interceptors.request.use((config) => {
	const token = document.querySelector("meta[name=csrf-token]")?.getAttribute("content");
	if (token) {
		config.headers["X-Frappe-CSRF-Token"] = token;
	}
	return config;
});

export const tourService = {
	getAllTours: () => api.get("/tours"),
	getTourById: (id) => api.get(`/tours/${id}`),
	createTourComment: (data) => api.post("/tour-comment", data),
	getTourComments: (tourId) => api.get(`/tour-comments/${tourId}`),
};

export const shopService = {
	getAllItems: () => api.get("/shop-items"),
	getItemById: (id) => api.get(`/shop-items/${id}`),
	createOrder: (data) => api.post("/create-order", data),
};

export const chatService = {
	getMessages: (userId) => api.get(`/messages/${userId}`),
	sendMessage: (data) => api.post("/send-message", data),
};

export const notificationService = {
	getSettings: () => api.get("/notification-settings"),
	updateSettings: (data) => api.put("/notification-settings", data),
};
