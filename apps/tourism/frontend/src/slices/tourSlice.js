import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { tourService } from "../services/api";

export const fetchTours = createAsyncThunk("tours/fetchTours", async () => {
	const response = await tourService.getAllTours();
	return response.data;
});

export const fetchTourById = createAsyncThunk("tours/fetchTourById", async (id) => {
	const response = await tourService.getTourById(id);
	return response.data;
});

export const createTourComment = createAsyncThunk("tours/createTourComment", async (data) => {
	const response = await tourService.createTourComment(data);
	return response.data;
});

const tourSlice = createSlice({
	name: "tours",
	initialState: {
		tours: [],
		currentTour: null,
		comments: [],
		loading: false,
		error: null,
	},
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchTours.pending, (state) => {
				state.loading = true;
			})
			.addCase(fetchTours.fulfilled, (state, action) => {
				state.loading = false;
				state.tours = action.payload;
			})
			.addCase(fetchTours.rejected, (state, action) => {
				state.loading = false;
				state.error = action.error.message;
			})
			.addCase(fetchTourById.fulfilled, (state, action) => {
				state.currentTour = action.payload;
			})
			.addCase(createTourComment.fulfilled, (state, action) => {
				state.comments.push(action.payload);
			});
	},
});

export default tourSlice.reducer;
