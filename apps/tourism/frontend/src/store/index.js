import { configureStore } from "@reduxjs/toolkit";
import tourReducer from "./slices/tourSlice";
import shopReducer from "./slices/shopSlice";
import chatReducer from "./slices/chatSlice";
import authReducer from "./slices/authSlice";
import notificationReducer from "./slices/notificationSlice";

export const store = configureStore({
	reducer: {
		tours: tourReducer,
		shop: shopReducer,
		chat: chatReducer,
		auth: authReducer,
		notifications: notificationReducer,
	},
});

export default store;
