import frappe
from frappe import _
from frappe.utils import now_datetime

@frappe.whitelist()
def get_tours():
    """Get all available tours"""
    tours = frappe.get_all(
        "Tour",
        fields=["name", "title", "description", "price", "duration", "image"],
        filters={"published": 1}
    )
    return tours

@frappe.whitelist()
def get_tour(name):
    """Get tour details by name"""
    tour = frappe.get_doc("Tour", name)
    return tour.as_dict()

@frappe.whitelist()
def create_tour_comment(tour, rating, comment):
    """Create a new tour comment"""
    doc = frappe.new_doc("Tour Comment")
    doc.tour = tour
    doc.user = frappe.session.user
    doc.rating = rating
    doc.comment = comment
    doc.timestamp = now_datetime()
    doc.insert(ignore_permissions=True)
    return doc.as_dict()

@frappe.whitelist()
def get_shop_items():
    """Get all available shop items"""
    items = frappe.get_all(
        "Shop Item",
        fields=["name", "item_name", "description", "price", "image", "seller", "stock_quantity"],
        filters={"stock_quantity": [">", 0]}
    )
    return items

@frappe.whitelist()
def get_chat_messages(user):
    """Get chat messages for a user"""
    messages = frappe.get_all(
        "Chat Message",
        fields=["*"],
        filters=[
            ["sender", "in", [frappe.session.user, user]],
            ["receiver", "in", [frappe.session.user, user]]
        ],
        order_by="timestamp desc"
    )
    return messages

@frappe.whitelist()
def send_message(receiver, message, reference_doctype=None, reference_name=None):
    """Send a new chat message"""
    doc = frappe.new_doc("Chat Message")
    doc.sender = frappe.session.user
    doc.receiver = receiver
    doc.message = message
    doc.timestamp = now_datetime()
    if reference_doctype and reference_name:
        doc.reference_doctype = reference_doctype
        doc.reference_name = reference_name
    doc.insert(ignore_permissions=True)
    return doc.as_dict()

@frappe.whitelist()
def get_notification_settings():
    """Get notification settings for current user"""
    settings = frappe.get_value(
        "Notification Settings",
        {"user": frappe.session.user},
        ["*"],
        as_dict=True
    )
    return settings or {}
