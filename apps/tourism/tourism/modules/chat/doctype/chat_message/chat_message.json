{"actions": [], "allow_rename": 0, "creation": "2025-06-11 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["sender", "receiver", "message", "timestamp", "is_read", "reference_doctype", "reference_name"], "fields": [{"fieldname": "sender", "fieldtype": "Link", "in_list_view": 1, "label": "Sender", "options": "User", "reqd": 1}, {"fieldname": "receiver", "fieldtype": "Link", "in_list_view": 1, "label": "Receiver", "options": "User", "reqd": 1}, {"fieldname": "message", "fieldtype": "Text", "label": "Message", "reqd": 1}, {"default": "now", "fieldname": "timestamp", "fieldtype": "Datetime", "in_list_view": 1, "label": "Timestamp", "reqd": 1}, {"default": "0", "fieldname": "is_read", "fieldtype": "Check", "label": "<PERSON>"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference DocType", "options": "DocType"}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Reference Name", "options": "reference_doctype"}], "index_web_pages_for_search": 0, "links": [], "modified": "2025-06-11 10:00:00.000000", "modified_by": "Administrator", "module": "Cha<PERSON>", "name": "Chat Message", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}