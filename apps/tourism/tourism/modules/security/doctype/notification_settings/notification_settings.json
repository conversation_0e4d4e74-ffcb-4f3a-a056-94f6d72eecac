{"actions": [], "allow_rename": 1, "creation": "2025-06-11 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["user", "email_notifications", "push_notifications", "notification_preferences", "tour_updates", "chat_messages", "order_updates", "promotional_updates"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "unique": 1}, {"default": "1", "fieldname": "email_notifications", "fieldtype": "Check", "label": "Email Notifications"}, {"default": "1", "fieldname": "push_notifications", "fieldtype": "Check", "label": "Push Notifications"}, {"fieldname": "notification_preferences", "fieldtype": "Section Break", "label": "Notification Preferences"}, {"default": "1", "fieldname": "tour_updates", "fieldtype": "Check", "label": "Tour Updates"}, {"default": "1", "fieldname": "chat_messages", "fieldtype": "Check", "label": "Chat Messages"}, {"default": "1", "fieldname": "order_updates", "fieldtype": "Check", "label": "Order Updates"}, {"default": "1", "fieldname": "promotional_updates", "fieldtype": "Check", "label": "Promotional Updates"}], "index_web_pages_for_search": 0, "links": [], "modified": "2025-06-11 10:00:00.000000", "modified_by": "Administrator", "module": "Security", "name": "Notification Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC"}