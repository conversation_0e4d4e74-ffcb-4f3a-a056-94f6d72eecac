{"actions": [], "allow_rename": 1, "autoname": "field:category_name", "creation": "2025-06-11 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["category_name", "description"], "fields": [{"fieldname": "category_name", "fieldtype": "Data", "label": "Category Name", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-11 10:00:00.000000", "modified_by": "Administrator", "module": "Shopping", "name": "Item Category", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC"}