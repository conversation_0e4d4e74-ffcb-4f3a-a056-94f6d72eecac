{"actions": [], "allow_rename": 1, "autoname": "SHOP-ITEM-.####", "creation": "2025-06-11 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["item_name", "description", "price", "image", "column_break_1", "seller", "category", "stock_quantity", "section_break_1", "location_details"], "fields": [{"fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name", "reqd": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "reqd": 1}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price", "reqd": 1}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Image"}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "seller", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "User", "reqd": 1}, {"fieldname": "category", "fieldtype": "Link", "label": "Category", "options": "Item Category", "reqd": 1}, {"fieldname": "stock_quantity", "fieldtype": "Int", "label": "Stock Quantity", "reqd": 1}, {"fieldname": "section_break_1", "fieldtype": "Section Break", "label": "Location"}, {"fieldname": "location_details", "fieldtype": "Small Text", "label": "Location Details", "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-11 10:00:00.000000", "modified_by": "Administrator", "module": "Shopping", "name": "Shop Item", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Shop Seller", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}