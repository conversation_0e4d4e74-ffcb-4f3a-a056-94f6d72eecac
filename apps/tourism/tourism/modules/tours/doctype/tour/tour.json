{"actions": [], "allow_rename": 1, "autoname": "TOUR-.####", "creation": "2023-06-11 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["title", "description", "duration", "price", "location", "column_break_1", "status", "tour_guide", "max_participants", "section_break_1", "activities", "section_break_2", "tour_tips"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Tour Title", "reqd": 1}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "reqd": 1}, {"fieldname": "duration", "fieldtype": "Int", "label": "Duration (Days)", "reqd": 1}, {"fieldname": "price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Price", "reqd": 1}, {"fieldname": "location", "fieldtype": "Data", "label": "Location", "reqd": 1}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Draft\nActive\nCancelled"}, {"fieldname": "tour_guide", "fieldtype": "Link", "label": "Tour Guide", "options": "User", "reqd": 1}, {"fieldname": "max_participants", "fieldtype": "Int", "label": "Maximum Participants", "reqd": 1}, {"fieldname": "section_break_1", "fieldtype": "Section Break", "label": "Activities"}, {"fieldname": "activities", "fieldtype": "Table", "label": "Activities", "options": "Tour Activity"}, {"fieldname": "section_break_2", "fieldtype": "Section Break", "label": "Tour Tips"}, {"fieldname": "tour_tips", "fieldtype": "Table", "label": "Tour Tips", "options": "Tour Tip"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-06-11 10:00:00.000000", "modified_by": "Administrator", "module": "Tours", "name": "Tour", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}