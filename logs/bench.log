2025-04-09 10:24:43,258 DEBUG cd job-bench && python3 -m venv env
2025-04-09 10:24:45,397 DEBUG cd job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-04-09 10:24:47,409 DEBUG cd job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet wheel
2025-04-09 10:24:51,022 LOG Getting frappe
2025-04-09 10:24:51,022 DEBUG cd job-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-04-09 10:25:18,730 LOG Installing frappe
2025-04-09 10:25:18,731 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/frappe 
2025-04-09 10:26:13,496 DEBUG cd /home/<USER>/job-bench/apps/frappe && yarn install --check-files
2025-04-09 10:26:21,679 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 10:26:21,858 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 10:26:21,858 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 10:26:21,859 DEBUG cd job-bench && bench build
2025-04-09 10:26:22,001 INFO /home/<USER>/.local/bin/bench build
2025-04-09 10:26:39,010 LOG setting up backups
2025-04-09 10:26:39,026 LOG backups were set up
2025-04-09 10:26:39,026 INFO Bench job-bench initialized
2025-04-09 10:27:55,487 INFO /home/<USER>/.local/bin/bench get-app erpnext --branch version-15
2025-04-09 10:27:56,670 LOG Getting erpnext
2025-04-09 10:27:56,670 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-04-09 10:28:12,047 LOG Installing erpnext
2025-04-09 10:28:12,047 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/erpnext 
2025-04-09 10:28:28,093 DEBUG cd /home/<USER>/job-bench/apps/erpnext && yarn install --check-files
2025-04-09 10:28:28,417 DEBUG bench build --app erpnext
2025-04-09 10:28:28,555 INFO /home/<USER>/.local/bin/bench build --app erpnext
2025-04-09 10:28:31,648 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 10:28:31,810 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 10:28:31,810 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 10:40:46,993 INFO /home/<USER>/.local/bin/bench new-site job
2025-04-09 10:43:53,817 INFO /home/<USER>/.local/bin/bench --site job add-to-hosts
2025-04-09 10:44:23,009 INFO /home/<USER>/.local/bin/bench --site install-app erpnext
2025-04-09 10:44:36,951 INFO /home/<USER>/.local/bin/bench --site job install-app erpnext
2025-04-09 10:46:03,318 INFO /home/<USER>/.local/bin/bench start
2025-04-09 10:46:03,596 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 10:46:03,601 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 10:46:03,607 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 10:46:03,615 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 12:00:01,642 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-09 12:48:14,152 INFO /home/<USER>/.local/bin/bench get-app hrms
2025-04-09 12:48:15,138 LOG Getting hrms
2025-04-09 12:48:15,138 DEBUG cd ./apps && git clone https://github.com/frappe/hrms.git  --depth 1 --origin upstream
2025-04-09 12:48:29,566 LOG Installing hrms
2025-04-09 12:48:29,567 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/hrms 
2025-04-09 12:48:31,520 DEBUG cd /home/<USER>/job-bench/apps/hrms && yarn install --check-files
2025-04-09 12:49:41,356 DEBUG bench build --app hrms
2025-04-09 12:49:41,484 INFO /home/<USER>/.local/bin/bench build --app hrms
2025-04-09 12:50:26,053 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 12:50:26,213 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 12:50:26,214 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 12:51:25,309 INFO /home/<USER>/.local/bin/bench get-app payments
2025-04-09 12:51:26,922 LOG Getting payments
2025-04-09 12:51:26,922 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git  --depth 1 --origin upstream
2025-04-09 12:51:28,879 LOG Installing payments
2025-04-09 12:51:28,880 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/payments 
2025-04-09 12:51:38,629 DEBUG bench build --app payments
2025-04-09 12:51:38,770 INFO /home/<USER>/.local/bin/bench build --app payments
2025-04-09 12:51:40,705 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 12:51:40,872 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 12:51:40,872 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 12:52:18,775 INFO /home/<USER>/.local/bin/bench --site job install-app payments
2025-04-09 12:52:36,773 INFO /home/<USER>/.local/bin/bench --site job install-app hrms --force
2025-04-09 12:53:45,513 INFO /home/<USER>/.local/bin/bench start
2025-04-09 12:53:45,752 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 12:53:45,780 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 12:53:45,798 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 12:53:45,799 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 12:54:06,387 INFO /home/<USER>/.local/bin/bench --site job install-app hrms --force
2025-04-09 12:55:44,237 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git
2025-04-09 12:55:44,243 LOG Getting csf_tz
2025-04-09 12:55:44,243 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git  --depth 1 --origin upstream
2025-04-09 12:55:49,791 LOG Installing csf_tz
2025-04-09 12:55:49,791 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/csf_tz 
2025-04-09 12:55:56,875 DEBUG cd /home/<USER>/job-bench/apps/csf_tz && yarn install --check-files
2025-04-09 12:55:57,798 DEBUG bench build --app csf_tz
2025-04-09 12:55:57,928 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-04-09 12:56:10,366 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 12:56:10,529 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 12:56:10,530 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 12:57:02,539 INFO /home/<USER>/.local/bin/bench get-app https://github.com/Emmafidelis/csf_tz.git --branch version-15
2025-04-09 12:57:02,545 LOG Getting csf_tz
2025-04-09 12:57:02,545 DEBUG cd ./apps && git clone https://github.com/Emmafidelis/csf_tz.git --branch version-15 --depth 1 --origin upstream
2025-04-09 12:57:06,550 LOG Installing csf_tz
2025-04-09 12:57:06,550 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/csf_tz 
2025-04-09 12:57:12,868 DEBUG cd /home/<USER>/job-bench/apps/csf_tz && yarn install --check-files
2025-04-09 12:57:13,550 DEBUG bench build --app csf_tz
2025-04-09 12:57:13,679 INFO /home/<USER>/.local/bin/bench build --app csf_tz
2025-04-09 12:57:15,493 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-04-09 12:57:15,662 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-04-09 12:57:15,662 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-04-09 12:57:35,924 INFO /home/<USER>/.local/bin/bench --site job install-app csf_tz --force
2025-04-09 12:58:27,273 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 12:58:52,437 INFO /home/<USER>/.local/bin/bench use job
2025-04-09 12:58:57,890 INFO /home/<USER>/.local/bin/bench start
2025-04-09 12:58:58,110 INFO /home/<USER>/.local/bin/bench worker
2025-04-09 12:58:58,116 INFO /home/<USER>/.local/bin/bench schedule
2025-04-09 12:58:58,136 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-04-09 12:58:58,158 INFO /home/<USER>/.local/bin/bench watch
2025-04-09 12:59:05,911 INFO /home/<USER>/.local/bin/bench migrate
2025-04-09 18:00:01,538 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 00:00:01,606 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 12:00:01,424 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-10 18:00:01,714 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-11 12:00:01,372 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-11 18:00:01,788 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-12 12:00:01,780 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-12 18:00:01,809 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-14 12:00:01,465 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-14 18:00:02,138 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 00:00:01,969 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 12:00:01,505 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-15 18:00:01,365 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-16 12:00:01,506 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-16 18:00:01,955 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-17 12:00:01,731 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-17 18:00:01,505 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-18 12:00:01,393 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-18 18:00:01,958 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-19 12:00:01,567 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-19 18:00:01,436 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-20 18:00:02,100 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-21 12:00:01,402 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-21 18:00:01,648 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-22 12:00:01,720 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-22 18:00:01,601 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-23 12:00:01,631 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-23 18:00:01,821 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-24 12:00:01,844 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-24 18:00:01,499 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-25 08:40:01,202 INFO /home/<USER>/anaconda3/bin/bench use job
2025-04-25 08:40:06,719 INFO /home/<USER>/anaconda3/bin/bench start
2025-04-25 08:40:07,031 INFO /home/<USER>/anaconda3/bin/bench serve --port 8000
2025-04-25 08:40:07,031 INFO /home/<USER>/anaconda3/bin/bench schedule
2025-04-25 08:40:07,042 INFO /home/<USER>/anaconda3/bin/bench worker
2025-04-25 08:40:07,045 INFO /home/<USER>/anaconda3/bin/bench watch
2025-04-25 10:18:08,931 INFO /home/<USER>/anaconda3/bin/bench migrate
2025-04-25 12:00:01,837 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-25 18:00:02,247 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-26 12:00:01,905 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-26 18:00:01,509 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-27 12:00:01,388 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-27 18:00:02,233 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-28 12:00:01,809 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-28 18:00:01,443 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-29 12:00:01,945 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-29 18:00:01,530 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-30 12:00:01,350 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-04-30 18:00:02,025 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-01 12:00:02,025 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-01 18:00:01,974 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-02 12:00:01,957 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-02 18:00:01,630 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-03 12:00:01,814 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-03 18:00:01,872 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-04 12:00:01,721 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-05 12:00:01,677 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-05 18:00:01,333 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-06 12:00:02,148 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-06 18:00:02,115 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 00:00:01,798 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 12:00:01,882 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-07 18:00:01,770 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-08 00:00:01,471 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-08 12:00:01,393 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-08 18:00:02,191 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-09 12:00:01,808 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-09 18:00:01,336 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-10 12:00:01,300 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-10 18:00:02,018 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-11 12:00:01,345 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-11 18:00:01,511 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-12 12:00:01,292 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-12 18:00:02,275 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-13 12:00:02,095 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-13 18:00:01,674 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-14 12:00:01,697 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-14 18:00:01,993 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-15 12:00:01,871 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-15 18:00:01,794 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-16 12:00:02,089 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-16 18:00:01,567 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-17 12:00:01,645 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-17 18:00:02,196 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-18 18:00:02,018 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-19 12:00:01,534 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-19 18:00:01,868 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-20 12:00:01,349 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-20 18:00:01,595 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-21 12:00:02,226 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-21 18:00:01,983 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-22 00:00:01,929 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-22 12:00:01,458 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-23 12:00:02,136 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-23 18:00:01,840 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-24 12:00:01,982 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-24 18:00:01,691 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-25 12:00:01,955 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-25 18:00:02,186 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-26 12:00:01,951 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-26 18:00:01,742 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-27 12:00:01,231 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-27 18:00:01,574 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-28 12:00:01,878 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-28 18:00:01,743 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-29 12:00:01,949 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-29 18:00:01,882 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-30 12:00:01,906 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-30 18:00:02,244 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-31 00:00:01,577 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-05-31 18:00:02,161 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-01 00:00:01,779 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-01 18:00:02,534 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-02 12:00:02,047 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-02 18:00:01,712 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 00:00:01,784 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 12:00:01,366 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-03 18:00:01,913 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-04 12:00:01,786 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-04 18:00:02,236 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-05 12:00:01,877 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-05 18:00:01,515 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-06 12:00:02,280 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-06 18:00:02,166 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-08 12:00:02,050 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-08 18:00:02,058 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-09 12:00:01,773 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-09 18:00:01,609 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-10 12:00:01,914 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-10 18:00:02,097 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 00:00:01,943 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 12:00:02,232 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-11 16:01:09,738 INFO /home/<USER>/.local/bin/bench use job
2025-06-11 16:01:15,490 INFO /home/<USER>/.local/bin/bench start
2025-06-11 16:01:15,781 INFO /home/<USER>/.local/bin/bench watch
2025-06-11 16:01:15,795 INFO /home/<USER>/.local/bin/bench worker
2025-06-11 16:01:15,812 INFO /home/<USER>/.local/bin/bench serve --port 8000
2025-06-11 16:01:15,818 INFO /home/<USER>/.local/bin/bench schedule
2025-06-11 16:01:25,980 INFO /home/<USER>/.local/bin/bench migrate
2025-06-11 16:28:41,863 INFO /home/<USER>/.local/bin/bench new-app tourism
2025-06-11 16:28:41,872 LOG creating new app tourism
2025-06-11 16:30:11,943 LOG Installing tourism
2025-06-11 16:30:11,953 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/tourism 
2025-06-11 16:30:15,693 DEBUG bench build --app tourism
2025-06-11 16:30:15,954 INFO /home/<USER>/.local/bin/bench build --app tourism
2025-06-11 16:30:26,886 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-06-11 16:30:27,110 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-11 16:30:27,110 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-11 16:30:28,097 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-11 16:37:55,790 INFO /home/<USER>/.local/bin/bench --site job make-module tourism
2025-06-11 18:00:01,579 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-12 12:00:01,656 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-12 18:00:01,264 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 00:00:02,171 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 12:00:01,640 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-13 18:00:02,294 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-14 18:00:01,530 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-15 12:00:01,896 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-15 18:00:01,818 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-16 12:00:01,900 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-16 18:00:02,203 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-17 12:00:02,209 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-17 18:00:02,289 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-18 12:00:01,814 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-18 18:00:02,096 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-19 12:00:01,742 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-19 18:00:01,580 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-20 12:00:02,167 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-20 18:00:02,317 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-21 12:00:01,706 INFO /home/<USER>/.local/bin/bench --verbose --site all backup
2025-06-21 21:52:14,401 INFO /home/<USER>/.local/bin/bench new-app tourism
2025-06-21 21:52:14,409 LOG creating new app tourism
2025-06-21 21:52:15,168 WARNING /home/<USER>/.local/bin/bench new-app tourism executed with exit code 1
2025-06-21 21:52:16,238 INFO A newer version of bench is available: 5.23.0 → 5.25.5
2025-06-21 21:53:44,203 INFO /home/<USER>/.local/bin/bench new-app tourism
2025-06-21 21:53:44,207 LOG creating new app tourism
2025-06-21 21:55:15,532 LOG Installing tourism
2025-06-21 21:55:15,534 DEBUG cd /home/<USER>/job-bench && /home/<USER>/job-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/job-bench/apps/tourism 
2025-06-21 21:55:19,278 DEBUG bench build --app tourism
2025-06-21 21:55:19,455 INFO /home/<USER>/.local/bin/bench build --app tourism
2025-06-21 21:55:26,995 DEBUG cd /home/<USER>/job-bench && sudo supervisorctl restart frappe:
2025-06-21 21:55:27,188 WARNING cd /home/<USER>/job-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-21 21:55:27,188 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-21 21:55:28,057 INFO A newer version of bench is available: 5.23.0 → 5.25.5
2025-06-21 21:55:39,760 INFO /home/<USER>/.local/bin/bench --site job install-app tourism
2025-06-21 21:56:15,343 INFO /home/<USER>/.local/bin/bench --site job make-doctype Tour Profile --module Tourism
