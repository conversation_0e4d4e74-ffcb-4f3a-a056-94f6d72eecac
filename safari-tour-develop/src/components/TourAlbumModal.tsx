import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Heart, MessageSquare, Upload, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { PhotoUploadModal } from "./PhotoUploadModal";

interface PhotoPost {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  tour: string;
  image: string;
  caption: string;
  location: string;
  likes: number;
  comments: number;
  timestamp: string;
  isLiked: boolean;
}

interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  rating: number;
  reviews: number;
  image: string;
  category: string;
}

interface TourAlbumModalProps {
  tour: Tour | null;
  isOpen: boolean;
  onClose: () => void;
}

export const TourAlbumModal = ({ tour, isOpen, onClose }: TourAlbumModalProps) => {
  const { toast } = useToast();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [commentingOnPost, setCommentingOnPost] = useState<string | null>(null);
  const [newComment, setNewComment] = useState("");

  // Tour-specific photos with sample data
  const getTourPhotos = (tourTitle: string): PhotoPost[] => {
    const photosByTour: { [key: string]: PhotoPost[] } = {
      "Serengeti Safari Adventure": [
        {
          id: "s1",
          user: { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", initials: "SJ" },
          tour: "Serengeti Safari Adventure",
          image: "https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=600&h=400&fit=crop",
          caption: "Amazing sunset during our Serengeti safari! The wildlife here is absolutely incredible. Saw the Big Five all in one day! 🦁🐘🦏🐃🐆",
          location: "Serengeti National Park",
          likes: 127,
          comments: 23,
          timestamp: "2 hours ago",
          isLiked: false
        },
        {
          id: "s2",
          user: { name: "David Wilson", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", initials: "DW" },
          tour: "Serengeti Safari Adventure",
          image: "https://images.unsplash.com/photo-1466721591366-2d5fba72006d?w=600&h=400&fit=crop",
          caption: "Witnessed the great migration! Thousands of wildebeest crossing the Mara River. Nature at its finest!",
          location: "Mara River, Serengeti",
          likes: 89,
          comments: 15,
          timestamp: "1 day ago",
          isLiked: true
        }
      ],
      "Mount Kilimanjaro Trek": [
        {
          id: "k1",
          user: { name: "Michael Chen", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", initials: "MC" },
          tour: "Mount Kilimanjaro Trek",
          image: "https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=600&h=400&fit=crop",
          caption: "Made it to the summit of Kilimanjaro! What an incredible journey. The views from Uhuru Peak are breathtaking.",
          location: "Uhuru Peak, Kilimanjaro",
          likes: 156,
          comments: 31,
          timestamp: "3 days ago",
          isLiked: false
        }
      ],
      "Zanzibar Beach Paradise": [
        {
          id: "z1",
          user: { name: "Emma Williams", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face", initials: "EW" },
          tour: "Zanzibar Beach Paradise",
          image: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=600&h=400&fit=crop",
          caption: "Paradise found! The beaches in Zanzibar are absolutely stunning. Crystal clear water and beautiful coral reefs.",
          location: "Nungwi Beach, Zanzibar",
          likes: 134,
          comments: 28,
          timestamp: "5 days ago",
          isLiked: true
        }
      ],
      "Ngorongoro Crater Safari": [
        {
          id: "n1",
          user: { name: "Alex Thompson", avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face", initials: "AT" },
          tour: "Ngorongoro Crater Safari",
          image: "https://images.unsplash.com/photo-1472396961693-142e6e269027?w=600&h=400&fit=crop",
          caption: "The Ngorongoro Crater is like a natural zoo! Saw lions, elephants, and rhinos all in one place. Incredible biodiversity!",
          location: "Ngorongoro Crater",
          likes: 98,
          comments: 19,
          timestamp: "1 week ago",
          isLiked: false
        }
      ]
    };

    return photosByTour[tourTitle] || [];
  };

  const [photos, setPhotos] = useState<PhotoPost[]>(tour ? getTourPhotos(tour.title) : []);

  const toggleLike = (postId: string) => {
    setPhotos(photos.map(photo => 
      photo.id === postId 
        ? { 
            ...photo, 
            isLiked: !photo.isLiked, 
            likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1 
          }
        : photo
    ));
  };

  const handleComment = (postId: string) => {
    if (newComment.trim()) {
      setPhotos(photos.map(photo => 
        photo.id === postId 
          ? { ...photo, comments: photo.comments + 1 }
          : photo
      ));
      
      toast({
        title: "Comment Added",
        description: "Your comment has been posted",
      });
      setNewComment("");
      setCommentingOnPost(null);
    }
  };

  const handlePhotoUpload = () => {
    // Refresh the photos when a new one is uploaded
    setIsUploadModalOpen(false);
  };

  if (!tour) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="text-2xl flex items-center gap-3">
              <img src={tour.image} alt={tour.title} className="w-12 h-12 rounded-lg object-cover" />
              {tour.title} - Photo Album
            </DialogTitle>
            <DialogDescription>
              Explore photos shared by travelers who experienced this amazing tour
            </DialogDescription>
          </DialogHeader>

          <div className="flex justify-between items-center mb-4">
            <Badge variant="outline" className="text-sm">
              {photos.length} Photos
            </Badge>
            <Button onClick={() => setIsUploadModalOpen(true)} size="sm">
              <Upload className="w-4 h-4 mr-2" />
              Add Photo
            </Button>
          </div>

          <div className="overflow-y-auto max-h-[60vh] space-y-6 pr-2">
            {photos.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-muted-foreground mb-4">
                  No photos have been shared for this tour yet.
                </div>
                <Button onClick={() => setIsUploadModalOpen(true)}>
                  <Upload className="w-4 h-4 mr-2" />
                  Be the first to share!
                </Button>
              </div>
            ) : (
              photos.map((photo) => (
                <div key={photo.id} className="border rounded-lg overflow-hidden bg-card">
                  <div className="p-4 pb-3">
                    <div className="flex items-center space-x-3 mb-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={photo.user.avatar} alt={photo.user.name} />
                        <AvatarFallback>{photo.user.initials}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm">{photo.user.name}</h4>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          {photo.location && <span>{photo.location}</span>}
                          <span>•</span>
                          <span>{photo.timestamp}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="aspect-[4/3] overflow-hidden">
                    <img
                      src={photo.image}
                      alt="Travel photo"
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  <div className="p-4">
                    <p className="text-sm text-foreground mb-3">{photo.caption}</p>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => toggleLike(photo.id)}
                          className="flex items-center space-x-1 hover:text-red-500 transition-colors"
                        >
                          <Heart 
                            className={`w-5 h-5 ${photo.isLiked ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`} 
                          />
                          <span className="text-sm font-medium">{photo.likes}</span>
                        </button>
                        
                        <div 
                          className="flex items-center space-x-1 text-muted-foreground cursor-pointer hover:text-foreground transition-colors" 
                          onClick={() => setCommentingOnPost(commentingOnPost === photo.id ? null : photo.id)}
                        >
                          <MessageSquare className="w-5 h-5" />
                          <span className="text-sm">{photo.comments}</span>
                        </div>
                      </div>
                    </div>

                    {commentingOnPost === photo.id && (
                      <div className="space-y-2 pt-3 border-t">
                        <Textarea
                          placeholder="Write a comment..."
                          value={newComment}
                          onChange={(e) => setNewComment(e.target.value)}
                          className="min-h-[60px] text-sm"
                        />
                        <div className="flex gap-2">
                          <Button 
                            onClick={() => handleComment(photo.id)}
                            size="sm"
                            className="h-8"
                          >
                            Post
                          </Button>
                          <Button 
                            onClick={() => setCommentingOnPost(null)}
                            variant="outline"
                            size="sm"
                            className="h-8"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      <PhotoUploadModal 
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={handlePhotoUpload}
      />
    </>
  );
};