// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://welgdactgdsybrekvofo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndlbGdkYWN0Z2RzeWJyZWt2b2ZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNDM5MDQsImV4cCI6MjA2NTcxOTkwNH0.ke3FpLCky5JAnRFPCbCwYZEwK-AujNz-QMh7ktaBp-k";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);