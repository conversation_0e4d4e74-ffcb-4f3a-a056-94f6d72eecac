import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, ThumbsUp, MessageSquare } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface Review {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  tour: string;
  rating: number;
  title: string;
  content: string;
  date: string;
  likes: number;
  comments: number;
  isLiked: boolean;
  helpful: boolean;
}

const Reviews = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch reviews from database
  const { data: reviews = [], isLoading } = useQuery({
    queryKey: ['reviews'],
    queryFn: async () => {
      const { data: reviewsData, error } = await supabase
        .from('reviews')
        .select(`
          *,
          profiles!inner(display_name, username, avatar_url)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform data and check user likes
      const reviewsWithLikes = await Promise.all(
        reviewsData.map(async (review) => {
          const { data: userLike } = await supabase
            .from('likes')
            .select('id')
            .eq('target_id', review.id)
            .eq('target_type', 'review')
            .eq('profile_id', user?.id || '')
            .single();

          const profile = review.profiles;
          return {
            id: review.id,
            user: {
              name: profile?.display_name || profile?.username || 'Anonymous',
              avatar: profile?.avatar_url || `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
              initials: (profile?.display_name || profile?.username || 'A').charAt(0).toUpperCase()
            },
            tour: review.tour,
            rating: review.rating,
            title: review.title,
            content: review.content,
            date: new Date(review.created_at).toLocaleDateString(),
            likes: review.likes_count,
            comments: review.comments_count,
            isLiked: !!userLike,
            helpful: review.helpful
          };
        })
      );

      return reviewsWithLikes;
    },
    enabled: !!user
  });

  const [newReview, setNewReview] = useState({
    tour: "",
    rating: 0,
    title: "",
    content: ""
  });

  const [filter, setFilter] = useState("all");

  // Toggle like mutation
  const toggleLikeMutation = useMutation({
    mutationFn: async ({ reviewId, isLiked }: { reviewId: string; isLiked: boolean }) => {
      if (isLiked) {
        const { error } = await supabase
          .from('likes')
          .delete()
          .eq('target_id', reviewId)
          .eq('target_type', 'review')
          .eq('profile_id', user!.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('likes')
          .insert({
            target_id: reviewId,
            target_type: 'review',
            profile_id: user!.id
          });
        if (error) throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
    }
  });

  const toggleLike = (reviewId: string) => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to like reviews",
        variant: "destructive"
      });
      return;
    }
    
    const review = reviews.find(r => r.id === reviewId);
    if (review) {
      toggleLikeMutation.mutate({ reviewId, isLiked: review.isLiked });
    }
  };

  // Submit review mutation
  const submitReviewMutation = useMutation({
    mutationFn: async (reviewData: typeof newReview) => {
      const { error } = await supabase
        .from('reviews')
        .insert({
          profile_id: user!.id,
          tour: reviewData.tour,
          rating: reviewData.rating,
          title: reviewData.title,
          content: reviewData.content
        });
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
      toast({
        title: "Review Submitted",
        description: "Thank you for your review!",
      });
      setNewReview({ tour: "", rating: 0, title: "", content: "" });
    }
  });

  const submitReview = () => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to submit a review",
        variant: "destructive"
      });
      return;
    }

    if (newReview.tour && newReview.rating && newReview.title && newReview.content) {
      submitReviewMutation.mutate(newReview);
    } else {
      toast({
        title: "Incomplete Review",
        description: "Please fill in all fields",
        variant: "destructive"
      });
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        }`}
      />
    ));
  };

  const filteredReviews = filter === "all" 
    ? reviews 
    : reviews.filter(review => review.tour === filter);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Tour Reviews & Experiences
          </h1>
          <p className="text-xl text-muted-foreground">
            Read what other travelers say about their Tanzania adventures
          </p>
        </div>

        {/* Write a Review Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Share Your Experience</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Select value={newReview.tour} onValueChange={(value) => setNewReview({...newReview, tour: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select a tour" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Serengeti Safari Adventure">Serengeti Safari Adventure</SelectItem>
                <SelectItem value="Mount Kilimanjaro Trek">Mount Kilimanjaro Trek</SelectItem>
                <SelectItem value="Zanzibar Beach Paradise">Zanzibar Beach Paradise</SelectItem>
                <SelectItem value="Ngorongoro Crater Safari">Ngorongoro Crater Safari</SelectItem>
              </SelectContent>
            </Select>

            <div>
              <label className="text-sm font-medium mb-2 block">Rating</label>
              <div className="flex space-x-1">
                {Array.from({ length: 5 }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => setNewReview({...newReview, rating: i + 1})}
                    className="hover:scale-110 transition-transform"
                  >
                    <Star
                      className={`w-6 h-6 ${
                        i < newReview.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      }`}
                    />
                  </button>
                ))}
              </div>
            </div>

            <input
              type="text"
              placeholder="Review title"
              value={newReview.title}
              onChange={(e) => setNewReview({...newReview, title: e.target.value})}
              className="w-full px-3 py-2 border border-border rounded-md bg-background"
            />

            <Textarea
              placeholder="Share your experience..."
              value={newReview.content}
              onChange={(e) => setNewReview({...newReview, content: e.target.value})}
              className="min-h-[120px]"
            />
          </CardContent>
          <CardFooter>
            <Button onClick={submitReview}>Submit Review</Button>
          </CardFooter>
        </Card>

        {/* Filter Reviews */}
        <div className="mb-6">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tours</SelectItem>
              <SelectItem value="Serengeti Safari Adventure">Serengeti Safari</SelectItem>
              <SelectItem value="Mount Kilimanjaro Trek">Kilimanjaro Trek</SelectItem>
              <SelectItem value="Zanzibar Beach Paradise">Zanzibar Beach</SelectItem>
              <SelectItem value="Ngorongoro Crater Safari">Ngorongoro Crater</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Reviews List */}
        <div className="space-y-6">
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading reviews...</p>
            </div>
          ) : filteredReviews.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No reviews yet. Be the first to share your experience!</p>
            </div>
          ) : (
            filteredReviews.map((review) => (
            <Card key={review.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={review.user.avatar} alt={review.user.name} />
                      <AvatarFallback>{review.user.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold text-foreground">{review.user.name}</h3>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{review.tour}</Badge>
                        <span className="text-sm text-muted-foreground">{review.date}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {renderStars(review.rating)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <h4 className="font-semibold text-lg mb-2">{review.title}</h4>
                <p className="text-muted-foreground leading-relaxed">{review.content}</p>
              </CardContent>
              <CardFooter className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => toggleLike(review.id)}
                    className="flex items-center space-x-1 hover:text-blue-500 transition-colors"
                  >
                    <ThumbsUp 
                      className={`w-4 h-4 ${review.isLiked ? 'fill-blue-500 text-blue-500' : 'text-muted-foreground'}`} 
                    />
                    <span className="text-sm">{review.likes}</span>
                  </button>
                  
                  <div className="flex items-center space-x-1 text-muted-foreground">
                    <MessageSquare className="w-4 h-4" />
                    <span className="text-sm">{review.comments}</span>
                  </div>
                  
                  {review.helpful && (
                    <Badge variant="secondary" className="text-xs">
                      Helpful
                    </Badge>
                  )}
                </div>
              </CardFooter>
            </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default Reviews;