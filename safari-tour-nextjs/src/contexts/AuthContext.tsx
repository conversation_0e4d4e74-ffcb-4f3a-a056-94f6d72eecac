'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { frappeAPI, TourProfile } from '@/lib/frappe-api'

interface User {
  email: string
  name: string
  full_name?: string
  user_image?: string
}

interface AuthContextType {
  user: User | null
  profile: TourProfile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  signUp: (email: string, password: string, fullName: string) => Promise<void>
  updateProfile: (data: Partial<TourProfile>) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<TourProfile | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const userData = await frappeAPI.getCurrentUser()
      if (userData && userData.name !== 'Guest') {
        setUser(userData)
        // Get user profile
        try {
          const profileData = await frappeAPI.getProfile()
          setProfile(profileData)
        } catch (error) {
          console.log('No profile found for user')
        }
      }
    } catch (error) {
      console.log('User not authenticated')
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      await frappeAPI.login(email, password)
      await checkUser()
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signOut = async () => {
    try {
      await frappeAPI.logout()
      setUser(null)
      setProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    // Note: Frappe user creation typically requires admin privileges
    // This would need to be implemented as a custom API endpoint
    throw new Error('User registration not implemented. Please contact administrator.')
  }

  const updateProfile = async (data: Partial<TourProfile>) => {
    try {
      const updatedProfile = await frappeAPI.updateProfile(data)
      setProfile(updatedProfile)
    } catch (error) {
      throw error
    }
  }

  const value = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    signUp,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
