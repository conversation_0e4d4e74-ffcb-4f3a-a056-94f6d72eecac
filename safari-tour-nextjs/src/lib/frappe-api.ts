// Frappe API client to replace Supabase integration
import { QueryClient } from '@tanstack/react-query'

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_FRAPPE_URL || 'http://localhost:8000'
const API_PREFIX = '/api/method/tourism'

// Types based on our Frappe doctypes
export interface TourProfile {
  name: string
  user: string
  username: string
  display_name?: string
  avatar_url?: string
  bio?: string
  is_seller: boolean
  is_tour_guide: boolean
  role: 'Tourist' | 'Tour Guide' | 'Seller' | 'Admin'
  created_at: string
  updated_at: string
}

export interface TourPost {
  name: string
  tour_profile: string
  tour: string
  caption: string
  image_url: string
  location?: string
  likes_count: number
  comments_count: number
  created_at: string
  updated_at: string
}

export interface TourProduct {
  name: string
  name_field: string
  description: string
  category: string
  image_url: string
  seller_id: string
  in_stock: boolean
  price: number
  price_type?: string
  price_min?: number
  price_max?: number
  discount_percentage?: number
  promotion_text?: string
  promotion_expires_at?: string
  created_at: string
  updated_at: string
}

export interface TourReview {
  name: string
  tour_profile: string
  tour: string
  title: string
  content: string
  rating: number
  helpful: boolean
  likes_count: number
  comments_count: number
  created_at: string
  updated_at: string
}

// API client class
class FrappeAPI {
  private baseURL: string
  private headers: HeadersInit

  constructor() {
    this.baseURL = API_BASE_URL + API_PREFIX
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
      credentials: 'include', // Important for Frappe session management
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.message || data
  }

  // Auth methods
  async login(email: string, password: string) {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ usr: email, pwd: password }),
    })
  }

  async logout() {
    return this.request('/auth/logout', { method: 'POST' })
  }

  async getCurrentUser() {
    return this.request('/auth/user')
  }

  // Profile methods
  async getProfile(username?: string): Promise<TourProfile> {
    const endpoint = username ? `/profile/${username}` : '/profile'
    return this.request(endpoint)
  }

  async createProfile(data: Partial<TourProfile>): Promise<TourProfile> {
    return this.request('/profile', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateProfile(data: Partial<TourProfile>): Promise<TourProfile> {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Posts methods
  async getPosts(limit = 20, offset = 0): Promise<TourPost[]> {
    return this.request(`/posts?limit=${limit}&offset=${offset}`)
  }

  async createPost(data: Partial<TourPost>): Promise<TourPost> {
    return this.request('/posts', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async likePost(postId: string): Promise<void> {
    return this.request(`/posts/${postId}/like`, { method: 'POST' })
  }

  async unlikePost(postId: string): Promise<void> {
    return this.request(`/posts/${postId}/unlike`, { method: 'POST' })
  }

  // Products methods
  async getProducts(category?: string): Promise<TourProduct[]> {
    const endpoint = category ? `/products?category=${category}` : '/products'
    return this.request(endpoint)
  }

  async getProduct(id: string): Promise<TourProduct> {
    return this.request(`/products/${id}`)
  }

  async createProduct(data: Partial<TourProduct>): Promise<TourProduct> {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Reviews methods
  async getReviews(tour?: string): Promise<TourReview[]> {
    const endpoint = tour ? `/reviews?tour=${tour}` : '/reviews'
    return this.request(endpoint)
  }

  async createReview(data: Partial<TourReview>): Promise<TourReview> {
    return this.request('/reviews', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // File upload method
  async uploadFile(file: File): Promise<{ file_url: string }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch(`${API_BASE_URL}/api/method/upload_file`, {
      method: 'POST',
      body: formData,
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.message
  }
}

// Export singleton instance
export const frappeAPI = new FrappeAPI()

// Query client for React Query
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
})
